// Import the functions you need from the SDKs you need
import { initializeApp, type FirebaseApp, type FirebaseOptions } from "firebase/app";
import { getAnalytics, isSupported } from "firebase/analytics";
import { getMessaging } from "firebase/messaging";
import { getAuth } from "firebase/auth";
import { env } from "$env/dynamic/public";
import { browser } from "$app/environment";
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
export const firebaseConfig: FirebaseOptions = JSON.parse(
    env.PUBLIC_GOOGLE_FIREBASE_CONFIG
);

let app: FirebaseApp;

export const fireapp = () => {
    let messaging;
    let auth;

    if (!app) {
        // Initialize Firebase
        app = initializeApp(firebaseConfig);
    }

    // Initialize Auth
    auth = getAuth(app);

    // Initialize Messaging
    messaging = getMessaging(app);

    return {
        app,
        messaging,
        auth
    }
    // throw new Error("YOUR PAGE IS NOT BROWSER");
}

// 异步初始化 Analytics
export const initAnalytics = async () => {
    if (browser && app) {
        try {
            if (await isSupported()) {
                const analytics = getAnalytics(app);
                console.log("Analytics initialized");
                return analytics;
            }
        } catch (error) {
            console.error("Failed to initialize Analytics:", error);
        }
    }
    throw new Error("YOUR PAGE IS NOT SUPPORT ANALYTICS");
}