<script lang="ts">
  import { Card } from "$lib/components/ui";
  import { Shield, Sparkles } from "@lucide/svelte";
  import AuthGuard from "./AuthGuard.svelte";

  interface Props {
    title: string;
    subtitle?: string;
    showLogo?: boolean;
    guard?: {
      ignoreAllCheck?: boolean;
      requireAuth?: boolean;
      requireEmailVerified?: boolean;
      requireActivated?: boolean;
      redirectTo?: string;
    };
    children?: any;
  }

  let { title, subtitle, showLogo = true, guard, children }: Props = $props();
</script>

<div
  class="h-screen overflow-hidden bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center"
>
  <!-- 背景装饰 -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div
      class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl"
    ></div>
    <div
      class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-purple-400/20 to-pink-400/20 rounded-full blur-3xl"
    ></div>
  </div>

  <div class="h-full w-full overflow-auto p-4 sm:p-6 lg:p-8">
    <div class="w-full max-w-md relative z-10 mx-auto">
      <!-- Logo和品牌信息 -->
      {#if showLogo}
        <div class="text-center mb-6 sm:mb-8">
          <a
            class="inline-flex items-center justify-center w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-2xl mb-3 sm:mb-4 shadow-lg"
            href="/"
          >
            <span class="text-xl sm:text-2xl">🍄</span>
          </a>
          <h1 class="text-xl sm:text-2xl font-bold text-gray-900 mb-2">
            蘑菇🍄 AI小说
          </h1>
          <p class="text-gray-600 text-sm">让每一次创作都充满无限可能</p>
        </div>
      {/if}

      <!-- 主要内容卡片 -->
      <Card
        variant="elevated"
        class="backdrop-blur-sm bg-white/80 border-0 shadow-xl"
      >
        <div class="p-6 sm:p-8">
          <!-- 页面标题 -->
          <div class="text-center mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-2">{title}</h2>
            {#if subtitle}
              <p class="text-gray-600 text-sm">{subtitle}</p>
            {/if}
          </div>

          <!-- 内容区域 -->
          <AuthGuard {...guard}>
            {@render children?.()}
          </AuthGuard>
        </div>
      </Card>

      <!-- 特性展示 -->
      <div class="mt-6 sm:mt-8 grid grid-cols-3 gap-3 sm:gap-4 text-center">
        <div class="bg-white/40 backdrop-blur-sm rounded-lg p-2 sm:p-3">
          <Sparkles class="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 mx-auto mb-1" />
          <p class="text-xs text-gray-600">AI创作助手</p>
        </div>
        <div class="bg-white/40 backdrop-blur-sm rounded-lg p-2 sm:p-3">
          <Shield class="w-4 h-4 sm:w-5 sm:h-5 text-green-600 mx-auto mb-1" />
          <p class="text-xs text-gray-600">安全可靠</p>
        </div>
        <div class="bg-white/40 backdrop-blur-sm rounded-lg p-2 sm:p-3">
          <span class="text-base sm:text-lg mx-auto mb-1 block">🍄</span>
          <p class="text-xs text-gray-600">创意无限</p>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  /* 自定义滚动条 */
  :global(html) {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  }

  :global(::-webkit-scrollbar) {
    width: 6px;
  }

  :global(::-webkit-scrollbar-track) {
    background: transparent;
  }

  :global(::-webkit-scrollbar-thumb) {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
  }

  :global(::-webkit-scrollbar-thumb:hover) {
    background-color: rgba(156, 163, 175, 0.7);
  }
</style>
