<script lang="ts">
  import { onMount } from "svelte";

  interface Props {
    items: any[];
    itemHeight: number;
    containerHeight: number;
    overscan?: number;
    children?: any;
  }

  let {
    items,
    itemHeight,
    containerHeight,
    overscan = 5,
    children,
  }: Props = $props();

  let scrollContainer: HTMLElement;
  let scrollTop = $state(0);
  let containerWidth = $state(0);

  // 计算可见范围
  const visibleStart = $derived(
    Math.max(0, Math.floor(scrollTop / itemHeight) - overscan),
  );
  const visibleEnd = $derived(
    Math.min(
      items.length,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan,
    ),
  );
  const visibleItems = $derived(items.slice(visibleStart, visibleEnd));

  // 计算总高度和偏移
  const totalHeight = $derived(items.length * itemHeight);
  const offsetY = $derived(visibleStart * itemHeight);

  function handleScroll(event: Event) {
    const target = event.target as HTMLElement;
    scrollTop = target.scrollTop;
  }

  onMount(() => {
    if (scrollContainer) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          containerWidth = entry.contentRect.width;
        }
      });

      resizeObserver.observe(scrollContainer);

      return () => {
        resizeObserver.disconnect();
      };
    }
  });
</script>

<div
  bind:this={scrollContainer}
  class="virtual-list-container"
  style="max-height: {containerHeight}px; overflow-y: auto;"
  onscroll={handleScroll}
>
  <div class="virtual-list-spacer" style="height: {totalHeight}px;">
    <div
      class="virtual-list-content"
      style="transform: translateY({offsetY}px);"
    >
      {#each visibleItems as item, index (visibleStart + index)}
        <div class="virtual-list-item" style="height: {itemHeight}px;">
          {@render children?.({ item: item, index: visibleStart + index })}
        </div>
      {/each}
    </div>
  </div>
</div>

<style>
  .virtual-list-container {
    position: relative;
  }

  .virtual-list-spacer {
    position: relative;
  }

  .virtual-list-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
  }

  .virtual-list-item {
    display: flex;
    align-items: center;
  }
</style>
